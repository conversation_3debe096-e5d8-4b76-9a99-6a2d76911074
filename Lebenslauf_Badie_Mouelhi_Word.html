<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta name="ProgId" content="Word.Document">
    <meta name="Generator" content="Microsoft Word">
    <meta name="Originator" content="Microsoft Word">
    <title><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON></title>
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowRevisions/>
            <w:DoNotPrintRevisions/>
            <w:DoNotShowMarkup/>
            <w:DoNotShowComments/>
            <w:DoNotShowInsertionsAndDeletions/>
            <w:DoNotShowPropertyChanges/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        @page {
            margin: 1.5cm;
            size: A4;
        }

        body {
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 11pt;
            line-height: 1.5;
            color: #2c2c2c;
            margin: 0;
            padding: 0;
        }

        .header {
            display: table;
            width: 100%;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            padding: 25pt;
            margin-bottom: 25pt;
            border-radius: 8pt;
        }

        .header-left {
            display: table-cell;
            width: 70%;
            vertical-align: middle;
            padding-right: 20pt;
        }

        .header-right {
            display: table-cell;
            width: 30%;
            vertical-align: middle;
            text-align: center;
        }

        .profile-photo {
            width: 120pt;
            height: 120pt;
            border-radius: 50%;
            border: 4pt solid white;
            object-fit: cover;
        }

        .header h1 {
            font-size: 28pt;
            font-weight: bold;
            margin: 0 0 8pt 0;
            text-transform: uppercase;
            letter-spacing: 1.5pt;
        }

        .header h2 {
            font-size: 16pt;
            font-weight: 300;
            margin: 0;
            opacity: 0.9;
            letter-spacing: 1pt;
        }
        
        .main-content {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .left-column {
            display: table-cell;
            width: 38%;
            vertical-align: top;
            padding-right: 25pt;
            background: #f8f9fa;
            padding: 20pt;
            border-radius: 8pt;
            margin-right: 15pt;
        }

        .right-column {
            display: table-cell;
            width: 62%;
            vertical-align: top;
            padding-left: 15pt;
        }

        .section {
            margin-bottom: 25pt;
            page-break-inside: avoid;
        }

        .section h3 {
            font-size: 13pt;
            font-weight: bold;
            color: #1e3c72;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
            background: linear-gradient(90deg, #1e3c72, #2a5298);
            color: white;
            padding: 8pt 12pt;
            margin: 0 0 15pt 0;
            border-radius: 4pt;
        }
        
        .contact-info {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .contact-info li {
            margin-bottom: 8pt;
            font-size: 10pt;
            padding: 6pt 10pt;
            background: white;
            border-radius: 4pt;
            border-left: 4pt solid #1e3c72;
            box-shadow: 0 1pt 3pt rgba(0,0,0,0.1);
        }

        .contact-info strong {
            color: #1e3c72;
            font-weight: bold;
            display: block;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
        }

        .skills-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .skills-list li {
            margin-bottom: 6pt;
            font-size: 10pt;
            padding: 8pt 12pt;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            border-radius: 20pt;
            text-align: center;
            font-weight: 500;
        }
        
        .experience-item {
            margin-bottom: 20pt;
            page-break-inside: avoid;
            padding: 15pt;
            background: white;
            border-radius: 6pt;
            border-left: 5pt solid #1e3c72;
            box-shadow: 0 2pt 4pt rgba(0,0,0,0.1);
        }

        .date {
            font-weight: bold;
            color: white;
            font-size: 9pt;
            background: #1e3c72;
            padding: 4pt 8pt;
            border-radius: 12pt;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
        }

        .position {
            font-weight: bold;
            font-size: 12pt;
            margin: 8pt 0 4pt 0;
            color: #2c2c2c;
        }

        .company {
            font-style: italic;
            color: #666666;
            font-size: 10pt;
            margin-bottom: 8pt;
        }

        .description {
            font-size: 10pt;
            line-height: 1.4;
            color: #555555;
        }

        .profile-text {
            font-size: 11pt;
            line-height: 1.6;
            text-align: justify;
            padding: 20pt;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 8pt;
            border-left: 5pt solid #1e3c72;
            font-style: italic;
        }

        .languages li {
            margin-bottom: 8pt;
            font-size: 10pt;
            padding: 6pt 10pt;
            background: white;
            border-radius: 4pt;
            border-left: 4pt solid #1e3c72;
        }

        .languages strong {
            color: #1e3c72;
            display: block;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
        }
        
        @media print {
            .two-column {
                display: block;
            }
            .left-column, .right-column {
                display: block;
                width: 100%;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <h1>Badie Mouelhi</h1>
            <h2>Ingenieur-Informatiker</h2>
        </div>
        <div class="header-right">
            <img src="badpic.png" alt="Badie Mouelhi" class="profile-photo">
        </div>
    </div>

    <div class="main-content">
        <div class="left-column">
            <div class="section">
                <h3>Kontaktdaten</h3>
                <ul class="contact-info">
                    <li><strong>Adresse:</strong><br>Ofen 25<br>St. Peter am Hart</li>
                    <li><strong>E-Mail:</strong><br><EMAIL></li>
                    <li><strong>Telefon:</strong><br>068181893288</li>
                    <li><strong>Geburtsdatum:</strong><br>07.09.1985</li>
                    <li><strong>Staatsbürgerschaft:</strong><br>Tunesien</li>
                    <li><strong>Familienstand:</strong><br>verheiratet</li>
                </ul>
            </div>
            
            <div class="section">
                <h3>Sprachen</h3>
                <ul class="languages">
                    <li><strong>Arabisch:</strong> Muttersprache</li>
                    <li><strong>Französisch:</strong> Sehr gut</li>
                    <li><strong>Englisch:</strong> Gut</li>
                    <li><strong>Deutsch:</strong> A2</li>
                </ul>
            </div>
            
            <div class="section">
                <h3>Persönliche Kompetenzen</h3>
                <ul class="skills-list">
                    <li>Teamfähigkeit</li>
                    <li>Technisches Verständnis</li>
                    <li>Konzentrationsfähigkeit</li>
                    <li>Kreativität</li>
                    <li>Lernbereitschaft</li>
                    <li>Handwerkliches Geschick</li>
                    <li>Feinmotorische Geschicklichkeit</li>
                    <li>Bereitschaft zur Schichtarbeit</li>
                    <li>Hilfsbereitschaft</li>
                </ul>
            </div>
            
            <div class="section">
                <h3>Sonstiges</h3>
                <ul class="contact-info">
                    <li><strong>Führerschein:</strong> Klasse B</li>
                    <li><strong>Verfügbarkeit:</strong> Schichtarbeit möglich</li>
                </ul>
            </div>
            
            <div class="section">
                <h3>Interessen & Hobbys</h3>
                <ul class="skills-list">
                    <li>Hightech- und AI-Trends</li>
                    <li>Fotografie</li>
                    <li>Camping</li>
                    <li>Kochen</li>
                </ul>
            </div>
        </div>
        
        <div class="right-column">
            <div class="section">
                <h3>Profil</h3>
                <div class="profile-text">
                    Kommunikativer, freundlicher Ingenieur-Informatiker mit der Fähigkeit, sich auf jede Situation einzustellen. 
                    Ich suche eine Arbeitsstelle, bei der ich mein Engagement und meine Flexibilität unter Beweis stellen kann. 
                    Ich habe mich immer durch meine Produktivität und meine Fähigkeit, schnell zu lernen, ausgezeichnet.
                </div>
            </div>
            
            <div class="section">
                <h3>Beruflicher Werdegang</h3>
                
                <div class="experience-item">
                    <div class="date">01/2025 - 03/2025</div>
                    <div class="position">Deutschqualifizierung, Aktive Arbeitssuche</div>
                    <div class="company">BFI-Braunau / AMS</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">09/2024 - 12/2024</div>
                    <div class="position">Arbeitssuche</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">07/2024 - 09/2024</div>
                    <div class="position">Produktionsmitarbeiter</div>
                    <div class="company">Berglandmilch, Geinberg</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">04/2024 - 07/2024</div>
                    <div class="position">Arbeitssuche</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">09/2023 - 04/2024</div>
                    <div class="position">Produktionsmitarbeiter/Lager</div>
                    <div class="company">B&R Industrial Automation, Eggelsberg</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">03/2023 - 06/2023</div>
                    <div class="position">Kommissionierung</div>
                    <div class="company">Doppler, Braunau am Inn</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">08/2014 - 12/2021</div>
                    <div class="position">Selbstständiger Fotograf/Videoeditor</div>
                    <div class="company">Studio Yassmin, Tunis</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">09/2013 - 12/2021</div>
                    <div class="position">Selbstständiger Ingenieur für Informatik</div>
                    <div class="company">J@assmin-net, Tunis</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">02/2011 - 06/2013</div>
                    <div class="position">Webmaster</div>
                    <div class="company">Qmax-Trading, Tunis</div>
                </div>
            </div>
            
            <div class="section">
                <h3>Ausbildung</h3>
                
                <div class="experience-item">
                    <div class="date">2016</div>
                    <div class="position">Entwickler (Android, Cross-Platform)</div>
                    <div class="company">Tunis, Tunesien</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">2008 - 2011</div>
                    <div class="position">Ingenieur Informatik</div>
                    <div class="company">Najah, Tajerouine</div>
                </div>
                
                <div class="experience-item">
                    <div class="date">1997 - 2006</div>
                    <div class="position">Mathematik Matura, Hochschule</div>
                    <div class="company">Sekundarschule, Tajerouine</div>
                </div>
            </div>
            
            <div class="section">
                <h3>Berufliche Erfahrungen</h3>
                
                <div class="experience-item">
                    <div class="position">PC-Techniker</div>
                    <div class="description">
                        • Einrichten, Aufrüsten und Umrüsten von Computern<br>
                        • Installation von Software und Programmen<br>
                        • Sicherstellung eines störungsfreien Betriebs<br>
                        • Schnelle Fehlerbehebung bei technischen Problemen
                    </div>
                </div>
                
                <div class="experience-item">
                    <div class="position">Produktionsarbeiter/Lagerarbeiter</div>
                    <div class="description">
                        • Bedienung von Geräten, Maschinen und Anlagen<br>
                        • Etikettieren, Verpacken und Fließbandarbeit<br>
                        • Lagertätigkeiten (Kommissionieren, Einlagern)<br>
                        • Reinigung von Maschinen und Anlagen<br>
                        • Montage von Einzelteilen<br>
                        • Schichtarbeit und Sichtkontrolle von Produkten
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
