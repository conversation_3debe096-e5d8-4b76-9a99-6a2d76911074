#!/usr/bin/env python3
"""
Script to convert HTML CV to PDF using weasyprint
"""

import os
import sys

def create_pdf():
    try:
        # Try to import weasyprint
        from weasyprint import HTML, CSS
        from weasyprint.text.fonts import FontConfiguration
        
        # Read the HTML file
        with open('Lebenslauf_Badie_Mouelhi.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Create PDF-specific CSS
        pdf_css = CSS(string='''
            @page {
                size: A4;
                margin: 2cm;
            }
            
            body {
                font-family: 'Arial', 'Helvetica', sans-serif;
                font-size: 10pt;
                line-height: 1.4;
            }
            
            .cv-container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .header {
                background: #2c3e50 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .sidebar {
                background: #ecf0f1 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .skills-list li {
                background: #3498db !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .profile-text {
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        ''')
        
        # Create HTML object and generate PDF
        html_doc = HTML(string=html_content)
        html_doc.write_pdf('Lebenslauf_Badie_Mouelhi.pdf', stylesheets=[pdf_css])
        
        print("✅ PDF erfolgreich erstellt: Lebenslauf_Badie_Mouelhi.pdf")
        return True
        
    except ImportError:
        print("❌ weasyprint ist nicht installiert.")
        print("Installieren Sie es mit: pip install weasyprint")
        return False
    except Exception as e:
        print(f"❌ Fehler beim Erstellen der PDF: {e}")
        return False

def create_simple_pdf():
    """Alternative method using reportlab for simple PDF creation"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT
        
        # Create PDF document
        doc = SimpleDocTemplate("Lebenslauf_Badie_Mouelhi_Simple.pdf", pagesize=A4,
                              rightMargin=2*cm, leftMargin=2*cm,
                              topMargin=2*cm, bottomMargin=2*cm)
        
        # Get styles
        styles = getSampleStyleSheet()
        
        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=6,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2c3e50')
        )
        
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#666666')
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading3'],
            fontSize=12,
            spaceAfter=10,
            textColor=colors.HexColor('#2c3e50'),
            borderWidth=1,
            borderColor=colors.HexColor('#2c3e50'),
            borderPadding=3
        )
        
        # Build content
        story = []
        
        # Header
        story.append(Paragraph("BADIE MOUELHI", title_style))
        story.append(Paragraph("INGENIEUR-INFORMATIKER", subtitle_style))
        story.append(Spacer(1, 20))
        
        # Contact Information
        story.append(Paragraph("KONTAKTDATEN", heading_style))
        contact_data = [
            ["Adresse:", "Ofen 25, St. Peter am Hart"],
            ["E-Mail:", "<EMAIL>"],
            ["Telefon:", "068181893288"],
            ["Geburtsdatum:", "07.09.1985"],
            ["Staatsbürgerschaft:", "Tunesien"],
            ["Familienstand:", "verheiratet"]
        ]
        
        contact_table = Table(contact_data, colWidths=[4*cm, 10*cm])
        contact_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(contact_table)
        story.append(Spacer(1, 20))
        
        # Profile
        story.append(Paragraph("PROFIL", heading_style))
        profile_text = """Kommunikativer, freundlicher Ingenieur-Informatiker mit der Fähigkeit, 
        sich auf jede Situation einzustellen. Ich suche eine Arbeitsstelle, bei der ich mein 
        Engagement und meine Flexibilität unter Beweis stellen kann. Ich habe mich immer durch 
        meine Produktivität und meine Fähigkeit, schnell zu lernen, ausgezeichnet."""
        story.append(Paragraph(profile_text, styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Work Experience
        story.append(Paragraph("BERUFLICHER WERDEGANG", heading_style))
        
        work_experiences = [
            ("01/2025 - 03/2025", "Deutschqualifizierung, Aktive Arbeitssuche", "BFI-Braunau / AMS"),
            ("07/2024 - 09/2024", "Produktionsmitarbeiter", "Berglandmilch, Geinberg"),
            ("09/2023 - 04/2024", "Produktionsmitarbeiter/Lager", "B&R Industrial Automation, Eggelsberg"),
            ("03/2023 - 06/2023", "Kommissionierung", "Doppler, Braunau am Inn"),
            ("08/2014 - 12/2021", "Selbstständiger Fotograf/Videoeditor", "Studio Yassmin, Tunis"),
            ("09/2013 - 12/2021", "Selbstständiger Ingenieur für Informatik", "J@assmin-net, Tunis"),
            ("02/2011 - 06/2013", "Webmaster", "Qmax-Trading, Tunis")
        ]
        
        for date, position, company in work_experiences:
            story.append(Paragraph(f"<b>{date}</b>", styles['Normal']))
            story.append(Paragraph(f"<b>{position}</b>", styles['Normal']))
            if company:
                story.append(Paragraph(f"<i>{company}</i>", styles['Normal']))
            story.append(Spacer(1, 10))
        
        # Education
        story.append(Spacer(1, 20))
        story.append(Paragraph("AUSBILDUNG", heading_style))
        
        education = [
            ("2016", "Entwickler (Android, Cross-Platform)", "Tunis, Tunesien"),
            ("2008 - 2011", "Ingenieur Informatik", "Najah, Tajerouine"),
            ("1997 - 2006", "Mathematik Matura, Hochschule", "Sekundarschule, Tajerouine")
        ]
        
        for date, degree, institution in education:
            story.append(Paragraph(f"<b>{date}</b>", styles['Normal']))
            story.append(Paragraph(f"<b>{degree}</b>", styles['Normal']))
            story.append(Paragraph(f"<i>{institution}</i>", styles['Normal']))
            story.append(Spacer(1, 10))
        
        # Languages
        story.append(Spacer(1, 20))
        story.append(Paragraph("SPRACHEN", heading_style))
        languages_text = """<b>Arabisch:</b> Muttersprache<br/>
        <b>Französisch:</b> Sehr gut<br/>
        <b>Englisch:</b> Gut<br/>
        <b>Deutsch:</b> A2"""
        story.append(Paragraph(languages_text, styles['Normal']))
        
        # Skills
        story.append(Spacer(1, 20))
        story.append(Paragraph("PERSÖNLICHE KOMPETENZEN", heading_style))
        skills_text = """Teamfähigkeit • Technisches Verständnis • Konzentrationsfähigkeit • 
        Kreativität • Lernbereitschaft • Handwerkliches Geschick • Feinmotorische Geschicklichkeit • 
        Bereitschaft zur Schichtarbeit • Hilfsbereitschaft"""
        story.append(Paragraph(skills_text, styles['Normal']))
        
        # Build PDF
        doc.build(story)
        print("✅ Einfache PDF erfolgreich erstellt: Lebenslauf_Badie_Mouelhi_Simple.pdf")
        return True
        
    except ImportError:
        print("❌ reportlab ist nicht installiert.")
        print("Installieren Sie es mit: pip install reportlab")
        return False
    except Exception as e:
        print(f"❌ Fehler beim Erstellen der einfachen PDF: {e}")
        return False

if __name__ == "__main__":
    print("🔄 Erstelle PDF-Versionen des Lebenslaufs...")
    
    # Try weasyprint first (better quality)
    if not create_pdf():
        print("\n🔄 Versuche alternative PDF-Erstellung...")
        create_simple_pdf()
    
    print("\n📁 Verfügbare Dateien:")
    for file in os.listdir('.'):
        if file.startswith('Lebenslauf_Badie_Mouelhi'):
            print(f"   • {file}")
