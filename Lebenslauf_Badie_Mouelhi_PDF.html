<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>sla<PERSON> - <PERSON><PERSON></title>
    <style>
        @page {
            size: A4;
            margin: 1.5cm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Calibri', 'Arial', sans-serif;
            font-size: 11pt;
            line-height: 1.5;
            color: #2d3748;
            background: white;
        }

        .cv-container {
            max-width: 21cm;
            margin: 0 auto;
            background: white;
            padding: 0;
        }

        .header {
            display: table;
            width: 100%;
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            padding: 25pt;
            margin-bottom: 25pt;
            border-radius: 8pt;
            box-shadow: 0 4pt 12pt rgba(45, 90, 39, 0.15);
        }
        
        .header-left {
            display: table-cell;
            width: 70%;
            vertical-align: middle;
            padding-right: 20pt;
        }

        .header-right {
            display: table-cell;
            width: 30%;
            vertical-align: middle;
            text-align: center;
        }

        .profile-photo {
            width: 110pt;
            height: 110pt;
            border-radius: 50%;
            border: 4pt solid white;
            object-fit: cover;
            background: white;
            display: inline-block;
            box-shadow: 0 4pt 12pt rgba(0,0,0,0.2);
        }

        .photo-placeholder {
            width: 110pt;
            height: 110pt;
            border-radius: 50%;
            border: 4pt solid white;
            background: rgba(255,255,255,0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
            text-align: center;
            color: white;
            box-shadow: 0 4pt 12pt rgba(0,0,0,0.2);
        }

        .header h1 {
            font-size: 26pt;
            font-weight: 700;
            margin: 0 0 8pt 0;
            text-transform: uppercase;
            letter-spacing: 2pt;
            text-shadow: 0 2pt 4pt rgba(0,0,0,0.1);
        }

        .header h2 {
            font-size: 15pt;
            font-weight: 400;
            margin: 0;
            opacity: 0.95;
            letter-spacing: 1.2pt;
            text-shadow: 0 1pt 2pt rgba(0,0,0,0.1);
        }
        
        .main-content {
            display: table;
            width: 100%;
            table-layout: fixed;
        }
        
        .left-column {
            display: table-cell;
            width: 36%;
            vertical-align: top;
            padding-right: 25pt;
            background: linear-gradient(135deg, #f7f9f7, #eef2ee);
            padding: 20pt;
            border-radius: 8pt;
            margin-right: 15pt;
            border: 1pt solid #e2e8e2;
        }

        .right-column {
            display: table-cell;
            width: 64%;
            vertical-align: top;
            padding-left: 15pt;
        }

        .section {
            margin-bottom: 25pt;
            page-break-inside: avoid;
        }

        .section h3 {
            font-size: 12pt;
            font-weight: 600;
            color: white;
            text-transform: uppercase;
            letter-spacing: 0.8pt;
            background: linear-gradient(90deg, #2d5a27, #4a7c59);
            padding: 8pt 12pt;
            margin: 0 0 15pt 0;
            border-radius: 4pt;
            box-shadow: 0 2pt 6pt rgba(45, 90, 39, 0.2);
        }
        
        .contact-info {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .contact-info li {
            margin-bottom: 10pt;
            font-size: 10pt;
            padding: 8pt 10pt;
            background: white;
            border-radius: 4pt;
            border-left: 4pt solid #2d5a27;
            box-shadow: 0 1pt 3pt rgba(0,0,0,0.08);
        }

        .contact-info strong {
            color: #2d5a27;
            font-weight: 600;
            display: block;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.6pt;
            margin-bottom: 3pt;
        }

        .skills-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .skills-list li {
            margin-bottom: 6pt;
            font-size: 10pt;
            padding: 8pt 12pt;
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            border-radius: 18pt;
            text-align: center;
            font-weight: 500;
            box-shadow: 0 2pt 4pt rgba(45, 90, 39, 0.2);
        }
        
        .experience-item {
            margin-bottom: 18pt;
            page-break-inside: avoid;
            padding: 15pt;
            background: white;
            border-radius: 6pt;
            border-left: 5pt solid #2d5a27;
            box-shadow: 0 2pt 6pt rgba(0,0,0,0.08);
            border: 1pt solid #e8f0e8;
        }

        .date {
            font-weight: 600;
            color: white;
            font-size: 9pt;
            background: linear-gradient(90deg, #2d5a27, #4a7c59);
            padding: 4pt 8pt;
            border-radius: 12pt;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.6pt;
            margin-bottom: 8pt;
            box-shadow: 0 2pt 4pt rgba(45, 90, 39, 0.2);
        }
        
        .position {
            font-weight: bold;
            font-size: 11pt;
            margin: 4pt 0;
            color: #2c2c2c;
        }
        
        .company {
            font-style: italic;
            color: #666666;
            font-size: 9pt;
            margin-bottom: 6pt;
        }
        
        .description {
            font-size: 9pt;
            line-height: 1.3;
            color: #555555;
        }
        
        .profile-text {
            font-size: 11pt;
            line-height: 1.6;
            text-align: justify;
            padding: 18pt;
            background: linear-gradient(135deg, #f7f9f7, #eef2ee);
            border-radius: 8pt;
            border-left: 5pt solid #2d5a27;
            font-style: italic;
            margin-bottom: 25pt;
            border: 1pt solid #e2e8e2;
            box-shadow: 0 2pt 6pt rgba(0,0,0,0.05);
        }

        .languages li {
            margin-bottom: 8pt;
            font-size: 10pt;
            padding: 8pt 10pt;
            background: white;
            border-radius: 4pt;
            border-left: 4pt solid #2d5a27;
            box-shadow: 0 1pt 3pt rgba(0,0,0,0.08);
        }

        .languages strong {
            color: #2d5a27;
            display: block;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.6pt;
            margin-bottom: 3pt;
            font-weight: 600;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .cv-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .header {
                background: linear-gradient(135deg, #2d5a27, #4a7c59) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .skills-list li {
                background: linear-gradient(135deg, #2d5a27, #4a7c59) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .section h3 {
                background: linear-gradient(90deg, #2d5a27, #4a7c59) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .date {
                background: linear-gradient(90deg, #2d5a27, #4a7c59) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .left-column {
                background: linear-gradient(135deg, #f7f9f7, #eef2ee) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .profile-text {
                background: linear-gradient(135deg, #f7f9f7, #eef2ee) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(45, 90, 39, 0.3);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .print-button:hover {
            background: linear-gradient(135deg, #4a7c59, #2d5a27);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(45, 90, 39, 0.4);
        }
        
        @media print {
            .print-button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">📄 Als PDF speichern</button>
    
    <div class="cv-container">
        <div class="header">
            <div class="header-left">
                <h1>Badie Mouelhi</h1>
                <h2>Informatik-Ingenieur</h2>
            </div>
            <div class="header-right">
                <img src="badpic.png" alt="Badie Mouelhi" class="profile-photo" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="photo-placeholder" style="display: none;">
                    Foto<br>badpic.png
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="left-column">
                <div class="section">
                    <h3>Kontaktdaten</h3>
                    <ul class="contact-info">
                        <li><strong>Adresse</strong>Ofen 25<br>St. Peter am Hart</li>
                        <li><strong>E-Mail</strong><EMAIL></li>
                        <li><strong>Telefon</strong>068181893288</li>
                        <li><strong>Geburtsdatum</strong>07.09.1985</li>
                        <li><strong>Staatsbürgerschaft</strong>Tunesien</li>
                        <li><strong>Familienstand</strong>verheiratet</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h3>Sprachen</h3>
                    <ul class="languages">
                        <li><strong>Arabisch</strong>Muttersprache</li>
                        <li><strong>Französisch</strong>Sehr gut</li>
                        <li><strong>Englisch</strong>Gut</li>
                        <li><strong>Deutsch</strong>A2</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h3>Persönliche Kompetenzen</h3>
                    <ul class="skills-list">
                        <li>Teamfähigkeit</li>
                        <li>Technisches Verständnis</li>
                        <li>Konzentrationsfähigkeit</li>
                        <li>Kreativität</li>
                        <li>Lernbereitschaft</li>
                        <li>Handwerkliches Geschick</li>
                        <li>Feinmotorik</li>
                        <li>Schichtarbeit</li>
                        <li>Hilfsbereitschaft</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h3>Sonstiges</h3>
                    <ul class="contact-info">
                        <li><strong>Führerschein</strong>Klasse B</li>
                        <li><strong>Verfügbarkeit</strong>Schichtarbeit möglich</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h3>Interessen</h3>
                    <ul class="skills-list">
                        <li>Hightech & AI-Trends</li>
                        <li>Fotografie</li>
                        <li>Camping</li>
                        <li>Kochen</li>
                    </ul>
                </div>
            </div>
            
            <div class="right-column">
                <div class="section">
                    <h3>Profil</h3>
                    <div class="profile-text">
                        Kommunikativer und engagierter Informatik-Ingenieur mit ausgeprägter Anpassungsfähigkeit und technischem Verständnis.
                        Ich bringe umfangreiche Erfahrungen in der Produktion und IT-Branche mit und zeichne mich durch hohe Lernbereitschaft,
                        Zuverlässigkeit und Teamfähigkeit aus. Auf der Suche nach einer Position, in der ich meine vielseitigen Kompetenzen
                        und mein Engagement gewinnbringend einsetzen kann.
                    </div>
                </div>
                
                <div class="section">
                    <h3>Beruflicher Werdegang</h3>
                    
                    <div class="experience-item">
                        <div class="date">01/2025 - 03/2025</div>
                        <div class="position">Deutschqualifizierung, Aktive Arbeitssuche</div>
                        <div class="company">BFI-Braunau / AMS</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">09/2024 - 12/2024</div>
                        <div class="position">Arbeitssuche</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">07/2024 - 09/2024</div>
                        <div class="position">Produktionsmitarbeiter</div>
                        <div class="company">Berglandmilch, Geinberg</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">04/2024 - 07/2024</div>
                        <div class="position">Arbeitssuche</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">09/2023 - 04/2024</div>
                        <div class="position">Produktionsmitarbeiter/Lager</div>
                        <div class="company">B&R Industrial Automation, Eggelsberg</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">03/2023 - 06/2023</div>
                        <div class="position">Kommissionierung</div>
                        <div class="company">Doppler, Braunau am Inn</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">08/2014 - 12/2021</div>
                        <div class="position">Selbstständiger Fotograf/Videoeditor</div>
                        <div class="company">Studio Yassmin, Tunis</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">09/2013 - 12/2021</div>
                        <div class="position">Selbstständiger Informatik-Ingenieur</div>
                        <div class="company">J@assmin-net, Tunis</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">02/2011 - 06/2013</div>
                        <div class="position">Webmaster</div>
                        <div class="company">Qmax-Trading, Tunis</div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>Ausbildung</h3>
                    
                    <div class="experience-item">
                        <div class="date">2016</div>
                        <div class="position">Entwickler (Android, Cross-Platform)</div>
                        <div class="company">Tunis, Tunesien</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">2008 - 2011</div>
                        <div class="position">Studium Informatik-Ingenieurwesen</div>
                        <div class="company">Najah, Tajerouine</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">1997 - 2006</div>
                        <div class="position">Matura (Mathematik-Schwerpunkt)</div>
                        <div class="company">Sekundarschule, Tajerouine</div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>Berufliche Erfahrungen</h3>
                    
                    <div class="experience-item">
                        <div class="position">PC-Techniker</div>
                        <div class="description">
                            • Einrichten, Aufrüsten und Umrüsten von Computern<br>
                            • Installation von Software und Programmen<br>
                            • Sicherstellung eines störungsfreien Betriebs<br>
                            • Schnelle Fehlerbehebung bei technischen Problemen
                        </div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="position">Produktionsarbeiter/Lagerarbeiter</div>
                        <div class="description">
                            • Bedienung von Geräten, Maschinen und Anlagen<br>
                            • Etikettieren, Verpacken und Fließbandarbeit<br>
                            • Lagertätigkeiten (Kommissionieren, Einlagern)<br>
                            • Reinigung von Maschinen und Anlagen<br>
                            • Montage von Einzelteilen<br>
                            • Schichtarbeit und Sichtkontrolle von Produkten
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-print functionality
        function printToPDF() {
            window.print();
        }
        
        // Show print dialog on page load
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (confirm('Möchten Sie den Lebenslauf als PDF speichern?')) {
                    printToPDF();
                }
            }, 1000);
        });
    </script>
</body>
</html>
