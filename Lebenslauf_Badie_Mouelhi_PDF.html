<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>sla<PERSON> - <PERSON><PERSON></title>
    <style>
        @page {
            size: A4;
            margin: 1.5cm;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #2c2c2c;
            background: white;
        }
        
        .cv-container {
            max-width: 21cm;
            margin: 0 auto;
            background: white;
            padding: 0;
        }
        
        .header {
            display: table;
            width: 100%;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            padding: 20pt;
            margin-bottom: 20pt;
            border-radius: 6pt;
        }
        
        .header-left {
            display: table-cell;
            width: 70%;
            vertical-align: middle;
            padding-right: 15pt;
        }
        
        .header-right {
            display: table-cell;
            width: 30%;
            vertical-align: middle;
            text-align: center;
        }
        
        .profile-photo {
            width: 100pt;
            height: 100pt;
            border-radius: 50%;
            border: 3pt solid white;
            object-fit: cover;
            background: white;
            display: inline-block;
        }
        
        .photo-placeholder {
            width: 100pt;
            height: 100pt;
            border-radius: 50%;
            border: 3pt solid white;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 9pt;
            text-align: center;
            color: white;
        }
        
        .header h1 {
            font-size: 24pt;
            font-weight: bold;
            margin: 0 0 6pt 0;
            text-transform: uppercase;
            letter-spacing: 1.5pt;
        }
        
        .header h2 {
            font-size: 14pt;
            font-weight: 300;
            margin: 0;
            opacity: 0.9;
            letter-spacing: 1pt;
        }
        
        .main-content {
            display: table;
            width: 100%;
            table-layout: fixed;
        }
        
        .left-column {
            display: table-cell;
            width: 35%;
            vertical-align: top;
            padding-right: 20pt;
            background: #f8f9fa;
            padding: 15pt;
            border-radius: 6pt;
            margin-right: 10pt;
        }
        
        .right-column {
            display: table-cell;
            width: 65%;
            vertical-align: top;
            padding-left: 15pt;
        }
        
        .section {
            margin-bottom: 20pt;
            page-break-inside: avoid;
        }
        
        .section h3 {
            font-size: 11pt;
            font-weight: bold;
            color: white;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
            background: linear-gradient(90deg, #1e3c72, #2a5298);
            padding: 6pt 10pt;
            margin: 0 0 12pt 0;
            border-radius: 3pt;
        }
        
        .contact-info {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .contact-info li {
            margin-bottom: 8pt;
            font-size: 9pt;
            padding: 6pt 8pt;
            background: white;
            border-radius: 3pt;
            border-left: 3pt solid #1e3c72;
        }
        
        .contact-info strong {
            color: #1e3c72;
            font-weight: bold;
            display: block;
            font-size: 8pt;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
            margin-bottom: 2pt;
        }
        
        .skills-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .skills-list li {
            margin-bottom: 4pt;
            font-size: 9pt;
            padding: 6pt 10pt;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            border-radius: 15pt;
            text-align: center;
            font-weight: 500;
        }
        
        .experience-item {
            margin-bottom: 15pt;
            page-break-inside: avoid;
            padding: 12pt;
            background: white;
            border-radius: 4pt;
            border-left: 4pt solid #1e3c72;
            box-shadow: 0 1pt 3pt rgba(0,0,0,0.1);
        }
        
        .date {
            font-weight: bold;
            color: white;
            font-size: 8pt;
            background: #1e3c72;
            padding: 3pt 6pt;
            border-radius: 10pt;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
            margin-bottom: 6pt;
        }
        
        .position {
            font-weight: bold;
            font-size: 11pt;
            margin: 4pt 0;
            color: #2c2c2c;
        }
        
        .company {
            font-style: italic;
            color: #666666;
            font-size: 9pt;
            margin-bottom: 6pt;
        }
        
        .description {
            font-size: 9pt;
            line-height: 1.3;
            color: #555555;
        }
        
        .profile-text {
            font-size: 10pt;
            line-height: 1.5;
            text-align: justify;
            padding: 15pt;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 6pt;
            border-left: 4pt solid #1e3c72;
            font-style: italic;
            margin-bottom: 20pt;
        }
        
        .languages li {
            margin-bottom: 6pt;
            font-size: 9pt;
            padding: 6pt 8pt;
            background: white;
            border-radius: 3pt;
            border-left: 3pt solid #1e3c72;
        }
        
        .languages strong {
            color: #1e3c72;
            display: block;
            font-size: 8pt;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
            margin-bottom: 2pt;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .cv-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .header {
                background: linear-gradient(135deg, #1e3c72, #2a5298) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .skills-list li {
                background: linear-gradient(135deg, #1e3c72, #2a5298) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .section h3 {
                background: linear-gradient(90deg, #1e3c72, #2a5298) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .date {
                background: #1e3c72 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .left-column {
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .profile-text {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1e3c72;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #2a5298;
        }
        
        @media print {
            .print-button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">📄 Als PDF speichern</button>
    
    <div class="cv-container">
        <div class="header">
            <div class="header-left">
                <h1>Badie Mouelhi</h1>
                <h2>Ingenieur-Informatiker</h2>
            </div>
            <div class="header-right">
                <img src="badpic.png" alt="Badie Mouelhi" class="profile-photo" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="photo-placeholder" style="display: none;">
                    Foto<br>badpic.png
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="left-column">
                <div class="section">
                    <h3>Kontaktdaten</h3>
                    <ul class="contact-info">
                        <li><strong>Adresse</strong>Ofen 25<br>St. Peter am Hart</li>
                        <li><strong>E-Mail</strong><EMAIL></li>
                        <li><strong>Telefon</strong>068181893288</li>
                        <li><strong>Geburtsdatum</strong>07.09.1985</li>
                        <li><strong>Staatsbürgerschaft</strong>Tunesien</li>
                        <li><strong>Familienstand</strong>verheiratet</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h3>Sprachen</h3>
                    <ul class="languages">
                        <li><strong>Arabisch</strong>Muttersprache</li>
                        <li><strong>Französisch</strong>Sehr gut</li>
                        <li><strong>Englisch</strong>Gut</li>
                        <li><strong>Deutsch</strong>A2</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h3>Persönliche Kompetenzen</h3>
                    <ul class="skills-list">
                        <li>Teamfähigkeit</li>
                        <li>Technisches Verständnis</li>
                        <li>Konzentrationsfähigkeit</li>
                        <li>Kreativität</li>
                        <li>Lernbereitschaft</li>
                        <li>Handwerkliches Geschick</li>
                        <li>Feinmotorik</li>
                        <li>Schichtarbeit</li>
                        <li>Hilfsbereitschaft</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h3>Sonstiges</h3>
                    <ul class="contact-info">
                        <li><strong>Führerschein</strong>Klasse B</li>
                        <li><strong>Verfügbarkeit</strong>Schichtarbeit möglich</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h3>Interessen</h3>
                    <ul class="skills-list">
                        <li>Hightech & AI-Trends</li>
                        <li>Fotografie</li>
                        <li>Camping</li>
                        <li>Kochen</li>
                    </ul>
                </div>
            </div>
            
            <div class="right-column">
                <div class="section">
                    <h3>Profil</h3>
                    <div class="profile-text">
                        Kommunikativer, freundlicher Ingenieur-Informatiker mit der Fähigkeit, sich auf jede Situation einzustellen. 
                        Ich suche eine Arbeitsstelle, bei der ich mein Engagement und meine Flexibilität unter Beweis stellen kann. 
                        Ich habe mich immer durch meine Produktivität und meine Fähigkeit, schnell zu lernen, ausgezeichnet.
                    </div>
                </div>
                
                <div class="section">
                    <h3>Beruflicher Werdegang</h3>
                    
                    <div class="experience-item">
                        <div class="date">01/2025 - 03/2025</div>
                        <div class="position">Deutschqualifizierung, Aktive Arbeitssuche</div>
                        <div class="company">BFI-Braunau / AMS</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">09/2024 - 12/2024</div>
                        <div class="position">Arbeitssuche</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">07/2024 - 09/2024</div>
                        <div class="position">Produktionsmitarbeiter</div>
                        <div class="company">Berglandmilch, Geinberg</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">04/2024 - 07/2024</div>
                        <div class="position">Arbeitssuche</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">09/2023 - 04/2024</div>
                        <div class="position">Produktionsmitarbeiter/Lager</div>
                        <div class="company">B&R Industrial Automation, Eggelsberg</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">03/2023 - 06/2023</div>
                        <div class="position">Kommissionierung</div>
                        <div class="company">Doppler, Braunau am Inn</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">08/2014 - 12/2021</div>
                        <div class="position">Selbstständiger Fotograf/Videoeditor</div>
                        <div class="company">Studio Yassmin, Tunis</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">09/2013 - 12/2021</div>
                        <div class="position">Selbstständiger Ingenieur für Informatik</div>
                        <div class="company">J@assmin-net, Tunis</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">02/2011 - 06/2013</div>
                        <div class="position">Webmaster</div>
                        <div class="company">Qmax-Trading, Tunis</div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>Ausbildung</h3>
                    
                    <div class="experience-item">
                        <div class="date">2016</div>
                        <div class="position">Entwickler (Android, Cross-Platform)</div>
                        <div class="company">Tunis, Tunesien</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">2008 - 2011</div>
                        <div class="position">Ingenieur Informatik</div>
                        <div class="company">Najah, Tajerouine</div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="date">1997 - 2006</div>
                        <div class="position">Mathematik Matura, Hochschule</div>
                        <div class="company">Sekundarschule, Tajerouine</div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>Berufliche Erfahrungen</h3>
                    
                    <div class="experience-item">
                        <div class="position">PC-Techniker</div>
                        <div class="description">
                            • Einrichten, Aufrüsten und Umrüsten von Computern<br>
                            • Installation von Software und Programmen<br>
                            • Sicherstellung eines störungsfreien Betriebs<br>
                            • Schnelle Fehlerbehebung bei technischen Problemen
                        </div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="position">Produktionsarbeiter/Lagerarbeiter</div>
                        <div class="description">
                            • Bedienung von Geräten, Maschinen und Anlagen<br>
                            • Etikettieren, Verpacken und Fließbandarbeit<br>
                            • Lagertätigkeiten (Kommissionieren, Einlagern)<br>
                            • Reinigung von Maschinen und Anlagen<br>
                            • Montage von Einzelteilen<br>
                            • Schichtarbeit und Sichtkontrolle von Produkten
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-print functionality
        function printToPDF() {
            window.print();
        }
        
        // Show print dialog on page load
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (confirm('Möchten Sie den Lebenslauf als PDF speichern?')) {
                    printToPDF();
                }
            }, 1000);
        });
    </script>
</body>
</html>
